export interface LLMProvider {
    name: string;
    models: string[];
    apiKey?: string;
    baseUrl?: string;
}
export interface DeepseekConfig extends LLMProvider {
    name: 'deepseek';
    models: ['deepseek-chat', 'deepseek-reasoner'];
    apiKey: string;
    baseUrl: 'https://api.deepseek.com';
}
export interface OllamaConfig extends LLMProvider {
    name: 'ollama';
    models: string[];
    baseUrl: string;
}
export interface CliConfig {
    currentProvider: 'deepseek' | 'ollama';
    currentModel: string;
    providers: {
        deepseek?: DeepseekConfig;
        ollama?: OllamaConfig;
    };
    sessionHistory: ChatMessage[];
    workingDirectory: string;
    autoApprove: boolean;
    maxRetries: number;
    retryDelay: number;
}
export interface ChatMessage {
    id: string;
    role: 'user' | 'assistant' | 'system' | 'tool';
    content: string;
    timestamp: Date;
    toolCalls?: ToolCall[];
    toolResults?: ToolResult[];
}
export interface ToolCall {
    id: string;
    name: string;
    arguments: Record<string, any>;
}
export interface ToolResult {
    id: string;
    name: string;
    result: any;
    success: boolean;
    error?: string;
    executionTime: number;
}
export interface ShellCommand {
    command: string;
    args?: string[];
    cwd?: string;
    timeout?: number;
    requireApproval?: boolean;
}
export interface ShellResult {
    success: boolean;
    stdout: string;
    stderr: string;
    exitCode: number;
    executionTime: number;
    command: string;
}
export interface ToolDefinition {
    name: string;
    description: string;
    parameters: {
        type: 'object';
        properties: Record<string, {
            type: string;
            description: string;
            enum?: string[];
            required?: boolean;
        }>;
        required: string[];
    };
    usage: {
        when_to_use: string;
        when_not_to_use: string;
        parallel_execution: boolean;
        sequential_execution: boolean;
        examples: string[];
    };
}
export interface RetryConfig {
    maxRetries: number;
    baseDelay: number;
    maxDelay: number;
    backoffMultiplier: number;
    retryableErrors: string[];
}
export interface TerminalState {
    isThinking: boolean;
    currentSession: string;
    messageHistory: ChatMessage[];
    workingDirectory: string;
    provider: string;
    model: string;
}
export interface SlashCommand {
    command: string;
    description: string;
    handler: (args: string[]) => Promise<void>;
}
export interface AnimationFrame {
    frame: string;
    duration: number;
}
//# sourceMappingURL=index.d.ts.map