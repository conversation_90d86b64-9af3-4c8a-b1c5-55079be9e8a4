import inquirer from 'inquirer';
import chalk from 'chalk';
import fs from 'fs-extra';
import path from 'path';
import { ConfigManager } from '../../config/ConfigManager.js';
import { SlashCommand, TerminalState } from '../../types/index.js';
import { StatusIndicator } from './ThinkingAnimation.js';
import { HeaderComponent } from './HeaderComponent.js';

export class SlashCommandsComponent {
  private configManager: ConfigManager;
  private state: TerminalState;

  constructor(configManager: ConfigManager, state: TerminalState) {
    this.configManager = configManager;
    this.state = state;
  }

  getCommands(): SlashCommand[] {
    return [
      {
        command: '/model',
        description: 'Switch to a different model',
        handler: this.handleModelCommand.bind(this)
      },
      {
        command: '/provider',
        description: 'Switch to a different provider (deepseek/ollama)',
        handler: this.handleProviderCommand.bind(this)
      },
      {
        command: '/session',
        description: 'Session management (new/clear/save)',
        handler: this.handleSessionCommand.bind(this)
      },
      {
        command: '/history',
        description: 'Show message history',
        handler: this.handleHistoryCommand.bind(this)
      },
      {
        command: '/system',
        description: 'Show system information',
        handler: this.handleSystemCommand.bind(this)
      },
      {
        command: '/help',
        description: 'Show help message',
        handler: this.handleHelpCommand.bind(this)
      },
      {
        command: '/exit',
        description: 'Exit the CLI',
        handler: this.handleExitCommand.bind(this)
      }
    ];
  }

  async executeCommand(input: string): Promise<boolean> {
    const parts = input.trim().split(' ');
    const command = parts[0];
    const args = parts.slice(1);

    const slashCommand = this.getCommands().find(cmd => cmd.command === command);
    
    if (slashCommand) {
      try {
        await slashCommand.handler(args);
        return true;
      } catch (error) {
        StatusIndicator.error(`Command failed: ${(error as Error).message}`);
        return true;
      }
    }

    return false;
  }

  private async handleModelCommand(args: string[]): Promise<void> {
    const config = this.configManager.getConfig();
    const currentProvider = config.providers[config.currentProvider];

    if (!currentProvider) {
      StatusIndicator.error('No provider configured');
      return;
    }

    if (args.length === 0) {
      // Show available models
      console.log(chalk.cyan('Available models:'));
      currentProvider.models.forEach((model) => {
        const indicator = model === config.currentModel ? chalk.green('●') : chalk.gray('○');
        console.log(`  ${indicator} ${model}`);
      });
      return;
    }

    const targetModel = args[0];
    if (!targetModel) {
      StatusIndicator.error('Please specify a model name');
      return;
    }

    if (!currentProvider.models.includes(targetModel)) {
      StatusIndicator.error(`Model '${targetModel}' not available for ${config.currentProvider}`);
      console.log(chalk.gray('Available models: ' + currentProvider.models.join(', ')));
      return;
    }

    await this.configManager.updateCurrentModel(targetModel);
    this.state.model = targetModel;
    StatusIndicator.success(`Switched to model: ${targetModel}`);
  }

  private async handleProviderCommand(args: string[]): Promise<void> {
    const config = this.configManager.getConfig();
    
    if (args.length === 0) {
      // Show available providers
      console.log(chalk.cyan('Available providers:'));
      Object.keys(config.providers).forEach(provider => {
        const indicator = provider === config.currentProvider ? chalk.green('●') : chalk.gray('○');
        console.log(`  ${indicator} ${provider}`);
      });
      return;
    }

    const targetProvider = args[0] as 'deepseek' | 'ollama';
    
    if (!['deepseek', 'ollama'].includes(targetProvider)) {
      StatusIndicator.error('Invalid provider. Available: deepseek, ollama');
      return;
    }

    if (!config.providers[targetProvider]) {
      StatusIndicator.error(`Provider '${targetProvider}' is not configured`);
      return;
    }

    await this.configManager.updateCurrentProvider(targetProvider);
    this.state.provider = targetProvider;
    this.state.model = config.providers[targetProvider]!.models[0] as string;
    
    StatusIndicator.success(`Switched to provider: ${targetProvider}`);
  }

  private async handleSessionCommand(args: string[]): Promise<void> {
    if (args.length === 0) {
      console.log(chalk.cyan('Session commands:'));
      console.log('  /session new    - Start a new session');
      console.log('  /session clear  - Clear current session');
      console.log('  /session save   - Save session to file');
      return;
    }

    const action = args[0];

    switch (action) {
      case 'new':
        await this.startNewSession();
        break;
      case 'clear':
        await this.clearSession();
        break;
      case 'save':
        await this.saveSession();
        break;
      default:
        StatusIndicator.error(`Unknown session action: ${action}`);
    }
  }

  private async startNewSession(): Promise<void> {
    const { confirm } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'confirm',
        message: 'Start a new session? Current session will be cleared.',
        default: false
      }
    ]);

    if (confirm) {
      await this.configManager.clearHistory();
      this.state.messageHistory = [];
      this.state.currentSession = `session_${Date.now()}`;
      StatusIndicator.success('New session started');
    }
  }

  private async clearSession(): Promise<void> {
    const { confirm } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'confirm',
        message: 'Clear current session? All messages will be lost.',
        default: false
      }
    ]);

    if (confirm) {
      await this.configManager.clearHistory();
      this.state.messageHistory = [];
      StatusIndicator.success('Session cleared');
    }
  }

  private async saveSession(): Promise<void> {
    if (this.state.messageHistory.length === 0) {
      StatusIndicator.warning('No messages to save');
      return;
    }

    const { filename } = await inquirer.prompt([
      {
        type: 'input',
        name: 'filename',
        message: 'Enter filename for session export:',
        default: `session_${new Date().toISOString().split('T')[0]}.json`,
        validate: (input) => {
          if (!input.trim()) {
            return 'Filename cannot be empty';
          }
          return true;
        }
      }
    ]);

    try {
      const sessionData = {
        sessionId: this.state.currentSession,
        provider: this.state.provider,
        model: this.state.model,
        timestamp: new Date().toISOString(),
        messageCount: this.state.messageHistory.length,
        messages: this.state.messageHistory
      };

      const filepath = path.resolve(filename);
      await fs.writeJSON(filepath, sessionData, { spaces: 2 });
      
      StatusIndicator.success(`Session saved to: ${filepath}`);
    } catch (error) {
      StatusIndicator.error(`Failed to save session: ${(error as Error).message}`);
    }
  }

  private async handleHistoryCommand(args: string[]): Promise<void> {
    const limit = args.length > 0 ? parseInt(args[0] || '10') : 10;
    
    if (this.state.messageHistory.length === 0) {
      StatusIndicator.info('No message history');
      return;
    }

    console.log(chalk.cyan(`\n📋 Message History (last ${limit} messages):\n`));
    
    const messages = this.state.messageHistory.slice(-limit);
    
    messages.forEach((message) => {
      const timestamp = new Date(message.timestamp).toLocaleTimeString();
      const roleColor = message.role === 'user' ? chalk.blue : 
                       message.role === 'assistant' ? chalk.green : 
                       chalk.gray;
      
      console.log(`${chalk.gray(`[${timestamp}]`)} ${roleColor(message.role.toUpperCase())}:`);
      console.log(`  ${message.content.substring(0, 100)}${message.content.length > 100 ? '...' : ''}`);
      
      if (message.toolCalls && message.toolCalls.length > 0) {
        console.log(chalk.yellow(`  🛠️  Tools used: ${message.toolCalls.map(tc => tc.name).join(', ')}`));
      }
      
      console.log();
    });
  }

  private async handleSystemCommand(_args: string[]): Promise<void> {
    console.log(HeaderComponent.renderSystemInfo());
    
    const config = this.configManager.getConfig();
    console.log(chalk.cyan('\nConfiguration:'));
    console.log(chalk.gray(`  Config file: ${this.configManager.getConfigPath()}`));
    console.log(chalk.gray(`  Current provider: ${config.currentProvider}`));
    console.log(chalk.gray(`  Current model: ${config.currentModel}`));
    console.log(chalk.gray(`  Auto-approve: ${config.autoApprove ? 'enabled' : 'disabled'}`));
    console.log(chalk.gray(`  Max retries: ${config.maxRetries}`));
    console.log(chalk.gray(`  Working directory: ${config.workingDirectory}`));
  }

  private async handleHelpCommand(_args: string[]): Promise<void> {
    console.log(HeaderComponent.renderHelp());
  }

  private async handleExitCommand(_args: string[]): Promise<void> {
    console.log(chalk.cyan('👋 Goodbye!'));
    process.exit(0);
  }

  isSlashCommand(input: string): boolean {
    return input.trim().startsWith('/');
  }
}
