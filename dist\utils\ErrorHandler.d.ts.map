{"version": 3, "file": "ErrorHandler.d.ts", "sourceRoot": "", "sources": ["../../src/utils/ErrorHandler.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,WAAW,EAAE,MAAM,mBAAmB,CAAC;AAEhD,qBAAa,YAAY;IACvB,OAAO,CAAC,WAAW,CAAc;gBAErB,WAAW,CAAC,EAAE,OAAO,CAAC,WAAW,CAAC;IAmBxC,gBAAgB,CAAC,CAAC,EACtB,SAAS,EAAE,MAAM,OAAO,CAAC,CAAC,CAAC,EAC3B,OAAO,GAAE,MAAoB,GAC5B,OAAO,CAAC,CAAC,CAAC;IA4Bb,OAAO,CAAC,gBAAgB;IA2BxB,OAAO,CAAC,cAAc;IAKtB,OAAO,CAAC,KAAK;IAIb,WAAW,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,EAAE,MAAM,GAAG,MAAM;IAKnD,QAAQ,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI;IAQ9C,UAAU,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI;IAKnD,OAAO,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI;IAKhD,UAAU,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI;IAKnD,uBAAuB,CAAC,KAAK,EAAE,KAAK,EAAE,WAAW,CAAC,EAAE,MAAM,EAAE,GAAG,KAAK;CA2BrE"}