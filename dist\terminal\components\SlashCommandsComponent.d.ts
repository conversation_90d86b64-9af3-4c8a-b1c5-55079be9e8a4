import { ConfigManager } from '../../config/ConfigManager.js';
import { SlashCommand, TerminalState } from '../../types/index.js';
export declare class SlashCommandsComponent {
    private configManager;
    private state;
    private deepseekProvider?;
    private ollamaProvider?;
    constructor(configManager: ConfigManager, state: TerminalState);
    private initializeProviders;
    getCommands(): SlashCommand[];
    executeCommand(input: string): Promise<boolean>;
    private handleModelCommand;
    private handleProviderCommand;
    private handleSessionCommand;
    private startNewSession;
    private clearSession;
    private saveSession;
    private handleHistoryCommand;
    private handleSystemCommand;
    private handleHelpCommand;
    private handleExitCommand;
    isSlashCommand(input: string): boolean;
}
//# sourceMappingURL=SlashCommandsComponent.d.ts.map