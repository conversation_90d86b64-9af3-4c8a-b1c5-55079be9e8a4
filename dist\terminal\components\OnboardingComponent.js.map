{"version": 3, "file": "OnboardingComponent.js", "sourceRoot": "", "sources": ["../../../src/terminal/components/OnboardingComponent.ts"], "names": [], "mappings": "AAAA,OAAO,QAAQ,MAAM,UAAU,CAAC;AAChC,OAAO,KAAK,MAAM,OAAO,CAAC;AAC1B,OAAO,MAAM,MAAM,QAAQ,CAAC;AAC5B,OAAO,QAAQ,MAAM,iBAAiB,CAAC;AACvC,OAAO,KAAK,MAAM,OAAO,CAAC;AAE1B,OAAO,EAAE,gBAAgB,EAAE,MAAM,qCAAqC,CAAC;AACvE,OAAO,EAAE,cAAc,EAAE,MAAM,mCAAmC,CAAC;AAEnE,OAAO,EAAE,eAAe,EAAE,iBAAiB,EAAE,MAAM,wBAAwB,CAAC;AAE5E,MAAM,OAAO,mBAAmB;IACtB,aAAa,CAAgB;IAErC,YAAY,aAA4B;QACtC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;IACrC,CAAC;IAED,KAAK,CAAC,KAAK;QACT,OAAO,CAAC,KAAK,EAAE,CAAC;QAChB,IAAI,CAAC,cAAc,EAAE,CAAC;QAEtB,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC;QAE9C,8BAA8B;QAC9B,IAAI,MAAM,CAAC,SAAS,CAAC,QAAQ,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;YACzD,MAAM,EAAE,aAAa,EAAE,GAAG,MAAM,QAAQ,CAAC,MAAM,CAAC;gBAC9C;oBACE,IAAI,EAAE,SAAS;oBACf,IAAI,EAAE,eAAe;oBACrB,OAAO,EAAE,2DAA2D;oBACpE,OAAO,EAAE,KAAK;iBACf;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,aAAa,EAAE,CAAC;gBACnB,OAAO;YACT,CAAC;QACH,CAAC;QAED,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;QAC5B,eAAe,CAAC,OAAO,CAAC,+CAA+C,CAAC,CAAC;QAEzE,+CAA+C;QAC/C,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;IAC1D,CAAC;IAEO,cAAc;QACpB,MAAM,KAAK,GAAG,MAAM,CAAC,QAAQ,CAAC,cAAc,EAAE;YAC5C,IAAI,EAAE,aAAa;YACnB,gBAAgB,EAAE,SAAS;YAC3B,cAAc,EAAE,SAAS;SAC1B,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;QACrC,OAAO,CAAC,GAAG,EAAE,CAAC;QAEd,MAAM,UAAU,GAAG,KAAK,CACtB,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,8BAA8B,CAAC;YAChD,KAAK,CAAC,IAAI,CAAC,kEAAkE,CAAC;YAC9E,KAAK,CAAC,IAAI,CAAC,uDAAuD,CAAC;YACnE,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC;YACzB,KAAK,CAAC,KAAK,CAAC,+CAA+C,CAAC;YAC5D,KAAK,CAAC,KAAK,CAAC,+CAA+C,CAAC;YAC5D,KAAK,CAAC,KAAK,CAAC,iDAAiD,CAAC;YAC9D,KAAK,CAAC,KAAK,CAAC,uDAAuD,CAAC;YACpE,KAAK,CAAC,KAAK,CAAC,wCAAwC,CAAC,EACrD;YACE,OAAO,EAAE,CAAC;YACV,MAAM,EAAE,CAAC;YACT,WAAW,EAAE,OAAO;YACpB,WAAW,EAAE,MAAM;YACnB,eAAe,EAAE,SAAS;SAC3B,CACF,CAAC;QAEF,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;IAC1B,CAAC;IAEO,KAAK,CAAC,cAAc;QAC1B,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,QAAQ,CAAC,MAAM,CAAC;YAC1C;gBACE,IAAI,EAAE,UAAU;gBAChB,IAAI,EAAE,WAAW;gBACjB,OAAO,EAAE,kDAAkD;gBAC3D,OAAO,EAAE;oBACP;wBACE,IAAI,EAAE,wCAAwC;wBAC9C,KAAK,EAAE,UAAU;wBACjB,OAAO,EAAE,IAAI;qBACd;oBACD;wBACE,IAAI,EAAE,8CAA8C;wBACpD,KAAK,EAAE,QAAQ;wBACf,OAAO,EAAE,KAAK;qBACf;iBACF;gBACD,QAAQ,EAAE,CAAC,KAAK,EAAE,EAAE;oBAClB,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;wBACvB,OAAO,qCAAqC,CAAC;oBAC/C,CAAC;oBACD,OAAO,IAAI,CAAC;gBACd,CAAC;aACF;SACF,CAAC,CAAC;QAEH,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;YACjC,IAAI,QAAQ,KAAK,UAAU,EAAE,CAAC;gBAC5B,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;YAC7B,CAAC;iBAAM,IAAI,QAAQ,KAAK,QAAQ,EAAE,CAAC;gBACjC,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;YAC3B,CAAC;QACH,CAAC;QAED,uBAAuB;QACvB,MAAM,EAAE,eAAe,EAAE,GAAG,MAAM,QAAQ,CAAC,MAAM,CAAC;YAChD;gBACE,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,iBAAiB;gBACvB,OAAO,EAAE,kDAAkD;gBAC3D,OAAO,EAAE,SAAS,CAAC,GAAG,CAAC,CAAC,CAAS,EAAE,EAAE,CAAC,CAAC;oBACrC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;oBAC5C,KAAK,EAAE,CAAC;iBACT,CAAC,CAAC;aACJ;SACF,CAAC,CAAC;QAEH,MAAM,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,eAAe,CAAC,CAAC;IAClE,CAAC;IAEO,KAAK,CAAC,aAAa;QACzB,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC,CAAC;QAElE,MAAM,YAAY,GAAG,KAAK,CACxB,KAAK,CAAC,KAAK,CAAC,iCAAiC,CAAC;YAC9C,KAAK,CAAC,IAAI,CAAC,2CAA2C,CAAC;YACvD,KAAK,CAAC,IAAI,CAAC,4CAA4C,CAAC;YACxD,KAAK,CAAC,IAAI,CAAC,wDAAwD,CAAC;YACpE,KAAK,CAAC,IAAI,CAAC,0CAA0C,CAAC,EACtD;YACE,OAAO,EAAE,CAAC;YACV,WAAW,EAAE,OAAO;YACpB,WAAW,EAAE,MAAM;SACpB,CACF,CAAC;QAEF,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QAE1B,MAAM,EAAE,MAAM,EAAE,cAAc,EAAE,iBAAiB,EAAE,GAAG,MAAM,QAAQ,CAAC,MAAM,CAAC;YAC1E;gBACE,IAAI,EAAE,UAAU;gBAChB,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE,8BAA8B;gBACvC,IAAI,EAAE,GAAG;gBACT,QAAQ,EAAE,CAAC,KAAK,EAAE,EAAE;oBAClB,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;wBACxC,OAAO,qBAAqB,CAAC;oBAC/B,CAAC;oBACD,OAAO,IAAI,CAAC;gBACd,CAAC;aACF;YACD;gBACE,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,mBAAmB;gBACzB,OAAO,EAAE,2CAA2C;gBACpD,OAAO,EAAE,KAAK;aACf;YACD;gBACE,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,gBAAgB;gBACtB,OAAO,EAAE,4BAA4B;gBACrC,OAAO,EAAE,0BAA0B;gBACnC,IAAI,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,iBAAiB;gBAC5C,QAAQ,EAAE,CAAC,KAAK,EAAE,EAAE;oBAClB,IAAI,CAAC;wBACH,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC;wBACf,OAAO,IAAI,CAAC;oBACd,CAAC;oBAAC,MAAM,CAAC;wBACP,OAAO,0BAA0B,CAAC;oBACpC,CAAC;gBACH,CAAC;aACF;SACF,CAAC,CAAC;QAEH,MAAM,MAAM,GAAmB;YAC7B,IAAI,EAAE,UAAU;YAChB,MAAM,EAAE,CAAC,eAAe,EAAE,mBAAmB,CAAC;YAC9C,MAAM,EAAE,MAAM,CAAC,IAAI,EAAE;YACrB,OAAO,EAAE,cAAc,IAAI,0BAA0B;SACtD,CAAC;QAEF,kBAAkB;QAClB,MAAM,QAAQ,GAAG,IAAI,iBAAiB,EAAE,CAAC;QACzC,QAAQ,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAC;QAE9C,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,gBAAgB,CAAC,MAAM,CAAC,CAAC;YAC9C,MAAM,OAAO,GAAG,MAAM,QAAQ,CAAC,kBAAkB,EAAE,CAAC;YAEpD,IAAI,OAAO,EAAE,CAAC;gBACZ,8BAA8B;gBAC9B,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,kBAAkB,EAAE,CAAC;gBACnD,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACtB,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC;gBACzB,CAAC;gBAED,QAAQ,CAAC,IAAI,EAAE,CAAC;gBAChB,eAAe,CAAC,OAAO,CAAC,iCAAiC,CAAC,CAAC;gBAE3D,MAAM,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;YAC9D,CAAC;iBAAM,CAAC;gBACN,QAAQ,CAAC,IAAI,EAAE,CAAC;gBAChB,eAAe,CAAC,KAAK,CAAC,+DAA+D,CAAC,CAAC;gBACvF,OAAO;YACT,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,QAAQ,CAAC,IAAI,EAAE,CAAC;YAChB,eAAe,CAAC,KAAK,CAAC,0BAA2B,KAAe,CAAC,OAAO,EAAE,CAAC,CAAC;YAC5E,OAAO;QACT,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,WAAW;QACvB,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC,CAAC;QAEhE,MAAM,UAAU,GAAG,KAAK,CACtB,KAAK,CAAC,KAAK,CAAC,+BAA+B,CAAC;YAC5C,KAAK,CAAC,IAAI,CAAC,6CAA6C,CAAC;YACzD,KAAK,CAAC,IAAI,CAAC,wCAAwC,CAAC;YACpD,KAAK,CAAC,IAAI,CAAC,8CAA8C,CAAC;YAC1D,KAAK,CAAC,IAAI,CAAC,mCAAmC,CAAC,EAC/C;YACE,OAAO,EAAE,CAAC;YACV,WAAW,EAAE,OAAO;YACpB,WAAW,EAAE,OAAO;SACrB,CACF,CAAC;QAEF,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAExB,MAAM,EAAE,QAAQ,EAAE,cAAc,EAAE,iBAAiB,EAAE,GAAG,MAAM,QAAQ,CAAC,MAAM,CAAC;YAC5E;gBACE,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,mBAAmB;gBACzB,OAAO,EAAE,8CAA8C;gBACvD,OAAO,EAAE,KAAK;aACf;YACD;gBACE,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,gBAAgB;gBACtB,OAAO,EAAE,wBAAwB;gBACjC,OAAO,EAAE,wBAAwB;gBACjC,IAAI,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,iBAAiB;gBAC5C,QAAQ,EAAE,CAAC,KAAK,EAAE,EAAE;oBAClB,IAAI,CAAC;wBACH,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC;wBACf,OAAO,IAAI,CAAC;oBACd,CAAC;oBAAC,MAAM,CAAC;wBACP,OAAO,0BAA0B,CAAC;oBACpC,CAAC;gBACH,CAAC;aACF;SACF,CAAC,CAAC;QAEH,MAAM,OAAO,GAAG,cAAc,IAAI,wBAAwB,CAAC;QAE3D,MAAM,MAAM,GAAiB;YAC3B,IAAI,EAAE,QAAQ;YACd,MAAM,EAAE,EAAE;YACV,OAAO;SACR,CAAC;QAEF,iCAAiC;QACjC,MAAM,QAAQ,GAAG,IAAI,iBAAiB,EAAE,CAAC;QACzC,QAAQ,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAE5C,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,cAAc,CAAC,MAAM,CAAC,CAAC;YAC5C,MAAM,OAAO,GAAG,MAAM,QAAQ,CAAC,kBAAkB,EAAE,CAAC;YAEpD,IAAI,OAAO,EAAE,CAAC;gBACZ,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,kBAAkB,EAAE,CAAC;gBACnD,QAAQ,CAAC,IAAI,EAAE,CAAC;gBAEhB,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACxB,eAAe,CAAC,OAAO,CAAC,oEAAoE,CAAC,CAAC;oBAC9F,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC,CAAC;oBACvD,MAAM,CAAC,MAAM,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,mBAAmB;gBACjD,CAAC;qBAAM,CAAC;oBACN,eAAe,CAAC,OAAO,CAAC,uCAAuC,MAAM,CAAC,MAAM,UAAU,CAAC,CAAC;oBACxF,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC;gBACzB,CAAC;gBAED,MAAM,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YAC5D,CAAC;iBAAM,CAAC;gBACN,QAAQ,CAAC,IAAI,EAAE,CAAC;gBAChB,eAAe,CAAC,KAAK,CAAC,2DAA2D,CAAC,CAAC;gBACnF,OAAO;YACT,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,QAAQ,CAAC,IAAI,EAAE,CAAC;YAChB,eAAe,CAAC,KAAK,CAAC,wBAAyB,KAAe,CAAC,OAAO,EAAE,CAAC,CAAC;YAC1E,OAAO;QACT,CAAC;IACH,CAAC;CACF"}